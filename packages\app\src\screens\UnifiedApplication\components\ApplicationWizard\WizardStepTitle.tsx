import React, { FC } from 'react'
import { useResponsive } from '../../../../utils/hooks'
import { useTranslation } from 'react-i18next'
import { StyleSheet, View } from 'react-native'
import { BtPlainText } from '@linqpal/components/src/ui'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { observer } from 'mobx-react'

export const WizardStepTitle: FC = observer(() => {
  const { sm } = useResponsive()
  const { t } = useTranslation('application')

  const store = useUnifiedApplication()

  const title = store.stepOptions?.title
  const titleStyle = store.stepOptions?.titleStyle

  if (!title) return null

  return (
    <View style={titleStyle}>
      <BtPlainText
        style={[styles.title, sm ? styles.titleDesktop : styles.titleMobile]}
      >
        {t(title as any)}
      </BtPlainText>
    </View>
  )
})

const styles = StyleSheet.create({
  title: {
    fontWeight: '600',
    color: '#003353',
    marginBottom: 10,
  },
  titleDesktop: {
    fontSize: 24,
    lineHeight: 36,
  },
  titleMobile: {
    fontSize: 18,
    lineHeight: 26,
  },
})
