import { observer } from 'mobx-react'
import { colors } from '@linqpal/components/src/theme'
import { StyleSheet, TouchableOpacity } from 'react-native'
import { BlackArrowBack, IconArrowBack } from '../../../../assets/icons'
import { BtText } from '@linqpal/components/src/ui'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { useResponsive } from '@linqpal/components/src/hooks'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'

export const WizardBackButton = observer(() => {
  const { sm } = useResponsive()
  const { t } = useTranslation('application')

  const store = useUnifiedApplication()

  const color = sm ? colors.accentText : colors.primary

  const handlePress = () => {
    const editorBackHandler = store.stepOptions.onMoveBack
    console.log(
      '=>(WIzardBackButton.tsx:21) editorBackHandler',
      editorBackHandler,
    )

    if (!editorBackHandler || !editorBackHandler(store)) {
      store.moveBack()
    }
  }

  return (
    <div
      style={{
        position: 'sticky',
        top: 0,
        zIndex: 1000,
        backgroundColor: 'white',
      }}
    >
      <TouchableOpacity
        style={styles.wrapper}
        onPress={handlePress}
        disabled={store.isSubmitting}
        testID="UnifiedApplication.Wizard.BackButton"
      >
        {sm ? (
          <IconArrowBack stroke={color} />
        ) : (
          <BlackArrowBack stroke={color} style={{ height: 20 }} />
        )}

        <BtText style={styles.label}>{t('Back')}</BtText>
      </TouchableOpacity>
    </div>
  )
})

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  label: {
    fontWeight: '500',
    color: colors.accentText,
    marginTop: -2,
    marginLeft: 15,
  },
})
