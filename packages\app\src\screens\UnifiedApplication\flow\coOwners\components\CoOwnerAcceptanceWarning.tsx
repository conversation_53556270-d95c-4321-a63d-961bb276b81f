import React, { FC } from 'react'
import { BtText } from '@linqpal/components/src/ui'
import { Trans, useTranslation } from 'react-i18next'
import { StyleSheet, Text, View } from 'react-native'
import { IconEmail } from '../../../../../assets/icons'

export const CoOwnerAcceptanceWarning: FC = () => {
  const { t } = useTranslation('application')

  return (
    <View style={styles.wrapper}>
      <IconEmail
        width={40}
        style={{
          marginLeft: 10,
          marginRight: 20,
          marginTop: 5,
        }}
      />

      <BtText style={styles.message}>
        <Trans
          t={t}
          i18nKey="CoOwners.AgreementWarning"
          components={{
            styled: <Text style={{ fontWeight: '700' }} />,
          }}
        />
      </BtText>
    </View>
  )
}

const styles = StyleSheet.create({
  wrapper: {
    width: '100%',
    borderRadius: 12,
    backgroundColor: '#F5F7F8',
    padding: 15,
    flexDirection: 'row',
  },
  message: {
    fontFamily: 'Inter',
    color: '#003353',
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '500',
  },
})
