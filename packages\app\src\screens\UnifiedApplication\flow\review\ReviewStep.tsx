import React, { FC, useEffect } from 'react'
import { StyleSheet, View } from 'react-native'
import { observer } from 'mobx-react'
import { UnifiedApplicationReview } from './components/UnifiedApplicationReview'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { UnifiedApplicationReviewStep } from '../../store/UnifiedApplicationReviewStore'
import { UnifiedApplicationPreview } from './components/UnifiedApplicationPreview'
import { UnifiedApplicationStore } from '../../store/UnifiedApplicationStore'
import { UnifiedApplicationAgreement } from './components/UnifiedApplicationAgreement'

const ReviewStepComponent: FC = () => {
  const store = useUnifiedApplication()

  useEffect(() => {
    switch (store.reviewStore.currentStep) {
      case UnifiedApplicationReviewStep.REVIEW:
        store.patchStepOptions({
          title: 'Review.Heading',
          canGoBack: false,
          showGroupTitle: true,
        })
        break
      case UnifiedApplicationReviewStep.PREVIEW:
        store.patchStepOptions({
          title: 'Preview.Heading',
          canGoBack: true,
          showGroupTitle: true,
        })
        break
      case UnifiedApplicationReviewStep.AGREEMENT:
        store.patchStepOptions({
          title: 'Agreement.Heading',
          canGoBack: true,
          showGroupTitle: false,
        })
        break
    }
  }, [store.reviewStore.currentStep])

  const renderCurrentState = () => {
    switch (store.reviewStore.currentStep) {
      case UnifiedApplicationReviewStep.REVIEW:
        return (
          <UnifiedApplicationReview
            doc={{} as any}
            flowController={{} as any}
          />
        )
      case UnifiedApplicationReviewStep.PREVIEW:
        return <UnifiedApplicationPreview />
      case UnifiedApplicationReviewStep.AGREEMENT:
        return <UnifiedApplicationAgreement />
      default:
        return null
    }
  }

  return <View style={styles.container}>{renderCurrentState()}</View>
}

export const ReviewStep = {
  options: {
    title: 'Review.Heading',
    titleStyle: { textAlign: 'center' },
    canGoBack: false,
    showNavigationButtons: false,
    onMoveBack: (store: UnifiedApplicationStore): boolean => {
      return store.reviewStore.moveBack()
    },
  },
  component: observer(ReviewStepComponent),
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tempButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 16,
    backgroundColor: '#f0f0f0',
  },
  tempButton: {
    flex: 1,
    marginHorizontal: 4,
  },
})
