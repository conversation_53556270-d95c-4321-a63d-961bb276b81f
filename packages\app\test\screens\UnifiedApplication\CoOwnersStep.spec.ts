import { CoOwnersStep } from '../../../src/screens/UnifiedApplication/flow/coOwners/CoOwnersStep'
import { UnifiedApplicationStore } from '../../../src/screens/UnifiedApplication/store/UnifiedApplicationStore'
import { CoOwner } from '../../../src/screens/UnifiedApplication/flow/coOwners/components/CoOwner'
import { OwnerTypes } from '@linqpal/models/src/dictionaries/UnifiedApplication'

describe('CoOwnersStep', () => {
  let store: UnifiedApplicationStore

  beforeEach(() => {
    store = new UnifiedApplicationStore()
  })

  describe('options', () => {
    it('should return default options when no currentCoOwner is set', () => {
      const options = typeof CoOwnersStep.options === 'function' 
        ? CoOwnersStep.options(store) 
        : CoOwnersStep.options

      expect(options.title).toBe('CoOwners.Title')
      expect(options.showNavigationButtons).toBe(true)
    })

    it('should return edit options when currentCoOwner is set', () => {
      // Create a mock coOwner
      const mockCoOwner = new CoOwner({
        id: 'test-id',
        firstName: 'John',
        lastName: 'Doe',
        percentOwned: 25,
        type: OwnerTypes.INDIVIDUAL
      })

      // Set currentCoOwner in the store
      store.coOwnersStore.edit(mockCoOwner)

      const options = typeof CoOwnersStep.options === 'function' 
        ? CoOwnersStep.options(store) 
        : CoOwnersStep.options

      expect(options.title).toBe('CoOwners.CoOwnerDetailsTitle')
      expect(options.showNavigationButtons).toBe(false)
    })

    it('should handle undefined store gracefully', () => {
      const options = typeof CoOwnersStep.options === 'function' 
        ? CoOwnersStep.options(undefined) 
        : CoOwnersStep.options

      expect(options.title).toBe('CoOwners.Title')
      expect(options.showNavigationButtons).toBe(true)
    })
  })

  describe('onMoveBack', () => {
    it('should cancel edit and return true when currentCoOwner is set', () => {
      const mockCoOwner = new CoOwner({
        id: 'test-id',
        firstName: 'John',
        lastName: 'Doe',
        percentOwned: 25,
        type: OwnerTypes.INDIVIDUAL
      })

      store.coOwnersStore.edit(mockCoOwner)
      expect(store.coOwnersStore.currentCoOwner).toBeTruthy()

      const result = CoOwnersStep.onMoveBack!(store)

      expect(result).toBe(true)
      expect(store.coOwnersStore.currentCoOwner).toBe(null)
    })

    it('should return false when no currentCoOwner is set', () => {
      expect(store.coOwnersStore.currentCoOwner).toBe(null)

      const result = CoOwnersStep.onMoveBack!(store)

      expect(result).toBe(false)
    })
  })
})
