import { ApplicationType } from '../../dictionaries/applicationType'
import { OwnerTypes } from '../../dictionaries/UnifiedApplication'

export interface IUnifiedApplicationOptions {
  type: ApplicationType
  userId: string
}

export interface ICoOwner {
  // common
  id: string
  type: typeof OwnerTypes.INDIVIDUAL | typeof OwnerTypes.ENTITY
  percentOwned: number
  address: string
  city: string
  state: string
  zip: string
  phone: string
  email: string
  // individual owner
  firstName: string
  lastName: string
  birthday: string
  ssn: string
  // entity
  entityName: string
  ein: string
}

export interface IUnifiedApplicationDraft {
  initialStep: string
  currentStep: string
  data: IUnifiedApplicationData
}

export interface IUnifiedApplicationData {
  businessInfo: {
    email?: string
    category?: string
    businessName?: {
      legalName?: string
      dba?: string
    }
    trade?: string
    businessPhone?: string
    businessAddress?: string
    startDate?: string
    type?: string
    ein?: string
  }
  finance: {
    revenue?: string
    debt?: string
    howMuchCredit?: string
    arAdvanceRequestedLimit?: string
  }
  businessOwner: {
    isOwner?: string
    ownershipPercentage?: number
    isAuthorized?: string
    authorizedDetails?: string
    address?: string
    birthdate?: string
    ssn?: string
  }
  coOwnerInfo: {
    coOwners?: ICoOwner[]
  }
  bank: {
    details?: string
  }
  review: {
    review?: string
    preview?: string
    agreement?: string
  }
}
