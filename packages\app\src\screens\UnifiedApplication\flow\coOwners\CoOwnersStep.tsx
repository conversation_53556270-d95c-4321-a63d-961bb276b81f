import React, { FC, useEffect } from 'react'
import { BtButton, BtPlainText } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react'
import { StyleSheet, View } from 'react-native'
import { useTranslation } from 'react-i18next'
import {
  FlowController,
  ModelUpdateInfo,
} from '../../../GeneralApplication/Application/FlowController'
import { Divider } from '@ui-kitten/components'
import { OwnerTypes } from '@linqpal/models/src/dictionaries/UnifiedApplication'
import { OwnerIcon } from '../../../GeneralApplication/Application/components/OwnerIcon'
import { CoOwnerEditor } from './components/CoOwnerEditor'
import { CoOwnersReview } from './components/CoOwnersReview'
import { AddCoOwnerButton } from './components/AddCoOwnerButton'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { useResponsive } from '../../../../utils/hooks'
import { CoOwnerValidator, ICoOwner } from '@linqpal/models'
import { UnifiedApplicationStore } from '../../store/UnifiedApplicationStore'

interface IProps {
  doc: any
  flowController: FlowController
  onValueUpdate: (updateInfo: ModelUpdateInfo) => void
}

const CoOwnersEditor: FC<IProps> = ({ doc, flowController, onValueUpdate }) => {
  const { t } = useTranslation('application')
  const { sm } = useResponsive()

  const store = useUnifiedApplication()
  const coOwnersStore = store.coOwnersStore

  const coOwners = store.document?.data?.coOwnerInfo?.coOwners || []

  useEffect(() => {
    console.log('current coowner', coOwnersStore.currentCoOwner)
    if (!coOwnersStore.currentCoOwner) {
      store.patchStepOptions({
        title: 'CoOwners.Title',
        showNavigationButtons: true,
      })
    } else {
      store.patchStepOptions({
        title: 'CoOwners.CoOwnerDetailsTitle',
        showNavigationButtons: false,
      })
    }
  }, [coOwnersStore.currentCoOwner, store])

  return (
    <>
      {store.isInReview ? (
        <CoOwnersReview />
      ) : (
        <>
          {coOwnersStore.currentCoOwner === null && (
            <>
              {coOwners.map((coOwner: ICoOwner) => (
                <>
                  <View style={styles.coOwnersList} key={coOwner.id}>
                    <OwnerIcon
                      isOwnerFilled={new CoOwnerValidator(
                        coOwner,
                      ).validateCoOwner()}
                    />

                    <View style={styles.coOwnerDetails}>
                      <BtPlainText
                        style={styles.coOwnerType}
                        onPress={() => coOwnersStore.edit(coOwner)}
                      >
                        {coOwner.type === OwnerTypes.INDIVIDUAL
                          ? t('CoOwners.IndividualOwner')
                          : t('CoOwners.EntityOwner')}
                      </BtPlainText>

                      {coOwner.firstName ||
                      coOwner.lastName ||
                      coOwner.entityName ? (
                        <BtPlainText
                          style={styles.ownerName}
                          onPress={() => coOwnersStore.edit(coOwner)}
                        >
                          {coOwner.type === OwnerTypes.INDIVIDUAL
                            ? `${coOwner.firstName} ${coOwner.lastName}`
                            : coOwner.entityName}
                        </BtPlainText>
                      ) : null}

                      <BtPlainText
                        style={styles.coOwnerPercentage}
                        onPress={() => coOwnersStore.edit(coOwner)}
                      >
                        {`${coOwner.percentOwned}% ${t(
                          'CoOwners.OwnerPercentagePostfix',
                        )}`}
                      </BtPlainText>
                    </View>

                    <View style={styles.coOwnerButtons}>
                      <BtButton
                        appearance={'ghost'}
                        size={'small'}
                        status={'basic'}
                        onPress={() => coOwnersStore.edit(coOwner)}
                        // eslint-disable-next-line i18next/no-literal-string
                        iconLeft="edit-outline"
                      >
                        {t('Review.Edit')}
                      </BtButton>
                      <BtButton
                        size="small"
                        appearance={'ghost'}
                        status={'basic'}
                        onPress={() => coOwnersStore.remove(coOwner)}
                        // eslint-disable-next-line i18next/no-literal-string
                        iconLeft="trash-2-outline"
                      >
                        {t('Bank.DeleteManualButton')}
                      </BtButton>
                    </View>
                  </View>
                  <Divider />
                </>
              ))}

              <View style={styles.footer}>
                <View style={styles.totalPercentageContainer}>
                  <BtPlainText style={styles.percentageAmount}>
                    {t('CoOwners.TotalPercentage')}
                  </BtPlainText>
                  <BtPlainText
                    size={16}
                    color={
                      store.document.totalOwnershipPercentage > 100
                        ? '#DB081C'
                        : ''
                    }
                  >
                    {store.document.totalOwnershipPercentage}%
                  </BtPlainText>
                </View>
                {sm && (
                  <AddCoOwnerButton onPress={() => coOwnersStore.createNew()} />
                )}
              </View>
              <Divider />

              {!sm && (
                <AddCoOwnerButton onPress={() => coOwnersStore.createNew()} />
              )}
            </>
          )}

          {coOwnersStore.currentCoOwner && <CoOwnerEditor />}
        </>
      )}
    </>
  )
}

const styles = StyleSheet.create({
  coOwnersList: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 15,
  },
  coOwnerDetails: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginTop: 10,
  },
  footer: {
    flexDirection: 'row',
    paddingTop: 20,
    paddingBottom: 25,
  },
  coOwnerType: {
    fontSize: 14,
    fontWeight: '400',
    color: 'gray',
  },
  ownerName: {
    fontSize: 16,
    fontWeight: '700',
    marginTop: 5,
  },
  coOwnerPercentage: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 5,
    marginBottom: 10,
  },
  coOwnerButtons: {
    flexDirection: 'row',
    marginLeft: 'auto',
  },
  totalPercentageContainer: {
    flexDirection: 'row',
    marginTop: 5,
  },
  percentageAmount: {
    fontSize: 16,
    fontWeight: '700',
  },
})

export const CoOwnersStep = {
  options: {
    title: 'CoOwners.Title',
  },
  component: observer(CoOwnersEditor),
  onMoveBack: (store: UnifiedApplicationStore) => {
    console.log('on move back coowner')
    if (store.coOwnersStore.currentCoOwner) {
      store.coOwnersStore.cancelEdit()
      store.tryGoBackToReview()

      return true
    }

    return false
  },
}
