import React, { FC } from 'react'
import { BtPlainText } from '@linqpal/components/src/ui'
import { SecureLabel } from '@linqpal/components/src/ui/BtSecureLabel'
import { ownerReviewStyles } from './ownerReviewStyles'
import { Col, Row } from '../../../../../ui/atoms/Grid'

interface OwnerPropertyRowProps {
  label: string
  value: string
  secured?: boolean
}

export const OwnerPropertyRow: FC<OwnerPropertyRowProps> = ({
  label,
  value,
  secured = false,
}) => {
  return (
    <Row>
      <Col style={{ flex: 1, width: 250 }}>
        <BtPlainText weight={500} style={ownerReviewStyles.propertyText}>
          {label}
        </BtPlainText>
      </Col>
      <Col style={{ flex: 2 }}>
        {secured && value ? (
          <SecureLabel value={value} />
        ) : (
          <BtPlainText weight={700} style={{ lineHeight: 26 }}>
            {value}
          </BtPlainText>
        )}
      </Col>
    </Row>
  )
}
