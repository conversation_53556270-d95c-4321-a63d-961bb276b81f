import { makeAutoObservable } from 'mobx'
import {
  IUnifiedApplicationData,
  IUnifiedApplicationDraft,
  IUnifiedApplicationOptions,
} from '@linqpal/models/src/types/unifiedApplication/IUnifiedApplicationDraft'
import { UnifiedApplicationDraftValidator } from '../../../../../models/src/types/unifiedApplication/UnifiedApplicationDraftValidator'

export class UnifiedApplicationDraft implements IUnifiedApplicationDraft {
  data: IUnifiedApplicationData

  initialStep: string

  currentStep: string

  private _validator: UnifiedApplicationDraftValidator

  constructor(data?: IUnifiedApplicationDraft) {
    this.initialStep = data?.initialStep || ''
    this.currentStep = data?.currentStep || data?.initialStep || ''

    this.data = {
      businessInfo: data?.data?.businessInfo || {},
      finance: data?.data?.finance || {},
      businessOwner: data?.data?.businessOwner || {},
      coOwnerInfo: data?.data?.coOwnerInfo || {},
      bank: data?.data?.bank || {},
      review: data?.data?.review || {},
    }

    this._validator = new UnifiedApplicationDraftValidator(this)

    makeAutoObservable(this)
  }

  get totalOwnershipPercentage(): number {
    const coOwners = this.data.coOwnerInfo.coOwners || []
    const businessOwnerPercentage =
      this.data.businessOwner.ownershipPercentage || 0

    return (
      coOwners.reduce((sum, coOwner) => sum + (coOwner.percentOwned || 0), 0) +
      businessOwnerPercentage
    )
  }

  validate(path: string, options: IUnifiedApplicationOptions): boolean {
    return this._validator.validate(path, options)
  }

  setField(path: string, value: any) {
    const [group, field] = path.split('.')

    // TODO: VK: Unified: mobx with strict mode require to use action to modify data. For now using this as workaround
    // find a better approach (each group as separate objects with getters and setters | strongly-typed path from ApplicationSteps | something else)
    this.data[group][field] = value
  }
}
